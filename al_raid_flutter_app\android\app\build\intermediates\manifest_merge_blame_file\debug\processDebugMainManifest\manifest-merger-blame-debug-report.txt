1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alraid.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:6:5-79
16-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:6:22-76
17    <uses-permission android:name="android.permission.CAMERA" /> <!-- Storage permissions for different Android versions -->
17-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:7:5-65
17-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:7:22-62
18    <uses-permission
18-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:10:5-11:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:10:22-77
20        android:maxSdkVersion="32" />
20-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:11:9-35
21    <uses-permission
21-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:12:5-13:38
22        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
22-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:12:22-78
23        android:maxSdkVersion="29" /> <!-- Media permissions for Android 13+ (API level 33+) -->
23-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:13:9-35
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:16:5-76
24-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:16:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Camera feature declaration -->
25-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:17:5-75
25-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-feature
26-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:20:5-85
27        android:name="android.hardware.camera"
27-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:20:19-57
28        android:required="false" />
28-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:20:58-82
29    <uses-feature
29-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:21:5-95
30        android:name="android.hardware.camera.autofocus"
30-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:21:19-67
31        android:required="false" />
31-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:21:68-92
32    <!--
33 Required to query activities that can process text, see:
34         https://developer.android.com/training/package-visibility and
35         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
36
37         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
38    -->
39    <queries>
39-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:60:5-65:15
40        <intent>
40-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:61:9-64:18
41            <action android:name="android.intent.action.PROCESS_TEXT" />
41-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:62:13-72
41-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:62:21-70
42
43            <data android:mimeType="text/plain" />
43-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:63:13-50
43-->C:\xampp\htdocs\ALraid\al_raid_flutter_app\android\app\src\main\AndroidManifest.xml:63:19-48
44        </intent>
45    </queries>
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.alraid.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.alraid.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="true"
58        android:icon="@mipmap/ic_launcher"
59        android:label="al_raid_app" >
60        <activity
61            android:name="com.alraid.app.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!--
87             Don't delete the meta-data below.
88             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
89        -->
90        <meta-data
91            android:name="flutterEmbedding"
92            android:value="2" />
93
94        <provider
94-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
95            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
95-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
96            android:authorities="com.alraid.app.flutter.image_provider"
96-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
97            android:exported="false"
97-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
98            android:grantUriPermissions="true" >
98-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
99            <meta-data
99-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
101                android:resource="@xml/flutter_image_picker_file_paths" />
101-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
102        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
103        <service
103-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
104            android:name="com.google.android.gms.metadata.ModuleDependencies"
104-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
105            android:enabled="false"
105-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
106            android:exported="false" >
106-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
107            <intent-filter>
107-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
108                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
108-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
108-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
109            </intent-filter>
110
111            <meta-data
111-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
112                android:name="photopicker_activity:0:required"
112-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
113                android:value="" />
113-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
114        </service>
115
116        <activity
116-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
117            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
117-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
118            android:exported="false"
118-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
119            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
119-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
120
121        <uses-library
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
122            android:name="androidx.window.extensions"
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
123            android:required="false" />
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
124        <uses-library
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
125            android:name="androidx.window.sidecar"
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
126            android:required="false" />
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
127
128        <provider
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.alraid.app.androidx-startup"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
