<?php
session_start();
require_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

// Check if admin is logged in
$isLoggedIn = isset($_SESSION['admin_id']);

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    $stmt = $db->prepare("SELECT * FROM admins WHERE username = ?");
    $stmt->execute([$username]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($admin && password_verify($password, $admin['password'])) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        header('Location: index.php');
        exit();
    } else {
        $loginError = 'Invalid credentials';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit();
}

// Handle product status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_product_status'])) {
    $productId = $_POST['product_id'];
    $status = $_POST['status'];

    $stmt = $db->prepare("UPDATE products SET status = ? WHERE id = ?");
    $stmt->execute([$status, $productId]);

    header('Location: index.php?tab=products');
    exit();
}

// Handle order status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_order_status'])) {
    $orderid = $_POST['order_id'];
    $status = $_POST['status'];

    $stmt = $db->prepare("UPDATE orders SET status = ? WHERE id = ?");
    $stmt->execute([$status, $orderid]);

    header('Location: index.php?tab=orders');
    exit();
}

// Handle user status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_user_status'])) {
    $userId = $_POST['user_id'];
    $status = $_POST['status'];

    $stmt = $db->prepare("UPDATE users SET status = ? WHERE id = ?");
    $stmt->execute([$status, $userId]);

    header('Location: index.php?tab=' . ($_GET['tab'] ?? 'users'));
    exit();
}

// Handle user deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    $userId = $_POST['user_id'];

    $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);

    header('Location: index.php?tab=users');
    exit();
}

if (!$isLoggedIn) {
    // Show login form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AL-Raid Admin Login</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #1976D2, #1565C0);
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .login-container {
                background: white;
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 400px;
            }
            .login-container h1 {
                text-align: center;
                color: #1976D2;
                margin-bottom: 2rem;
            }
            .form-group {
                margin-bottom: 1rem;
            }
            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: bold;
            }
            .form-group input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #ddd;
                border-radius: 5px;
                box-sizing: border-box;
            }
            .btn {
                width: 100%;
                padding: 0.75rem;
                background: #1976D2;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 1rem;
            }
            .btn:hover {
                background: #1565C0;
            }
            .error {
                color: red;
                text-align: center;
                margin-bottom: 1rem;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h1>AL-Raid Admin</h1>
            <?php if (isset($loginError)): ?>
                <div class="error"><?php echo $loginError; ?></div>
            <?php endif; ?>
            <form method="POST">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" name="login" class="btn">Login</button>
            </form>
            <p style="text-align: center; margin-top: 1rem; color: #666; font-size: 0.9rem;">
                Default: admin / admin123
            </p>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Get current tab
$currentTab = $_GET['tab'] ?? 'dashboard';

// Get statistics
$stmt = $db->query("SELECT COUNT(*) as total_users FROM users");
$totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];

$stmt = $db->query("SELECT COUNT(*) as total_merchants FROM users WHERE role = 'merchant'");
$totalMerchants = $stmt->fetch(PDO::FETCH_ASSOC)['total_merchants'];

$stmt = $db->query("SELECT COUNT(*) as total_buyers FROM users WHERE role = 'buyer'");
$totalBuyers = $stmt->fetch(PDO::FETCH_ASSOC)['total_buyers'];

$stmt = $db->query("SELECT COUNT(*) as pending_users FROM users WHERE status = 'pending'");
$pendingUsers = $stmt->fetch(PDO::FETCH_ASSOC)['pending_users'];

$stmt = $db->query("SELECT COUNT(*) as total_products FROM products");
$totalProducts = $stmt->fetch(PDO::FETCH_ASSOC)['total_products'];

$stmt = $db->query("SELECT COUNT(*) as total_orders FROM orders");
$totalOrders = $stmt->fetch(PDO::FETCH_ASSOC)['total_orders'];

$stmt = $db->query("SELECT COUNT(*) as pending_products FROM products WHERE status = 'under_review'");
$pendingProducts = $stmt->fetch(PDO::FETCH_ASSOC)['pending_products'];

$stmt = $db->query("SELECT COUNT(*) as pending_orders FROM orders WHERE status = 'under_review'");
$pendingOrders = $stmt->fetch(PDO::FETCH_ASSOC)['pending_orders'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AL-Raid Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: #1976D2;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            font-size: 1.5rem;
        }
        .header .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-tabs {
            background: white;
            border-bottom: 1px solid #ddd;
            padding: 0 2rem;
        }
        .nav-tabs ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-tabs a {
            display: block;
            padding: 1rem 0;
            text-decoration: none;
            color: #666;
            border-bottom: 3px solid transparent;
        }
        .nav-tabs a.active {
            color: #1976D2;
            border-bottom-color: #1976D2;
        }
        .content {
            padding: 2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-card h3 {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #1976D2;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        .btn-primary { background: #1976D2; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-under_review { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AL-Raid Admin Dashboard</h1>
        <div class="user-info">
            <span>Welcome, <?php echo $_SESSION['admin_username']; ?></span>
            <a href="?logout=1" class="btn btn-danger">Logout</a>
        </div>
    </div>

    <nav class="nav-tabs">
        <ul>
            <li><a href="?tab=dashboard" class="<?php echo $currentTab === 'dashboard' ? 'active' : ''; ?>">Dashboard</a></li>
            <li><a href="?tab=users" class="<?php echo $currentTab === 'users' ? 'active' : ''; ?>">All Users</a></li>
            <li><a href="?tab=merchants" class="<?php echo $currentTab === 'merchants' ? 'active' : ''; ?>">Merchants</a></li>
            <li><a href="?tab=buyers" class="<?php echo $currentTab === 'buyers' ? 'active' : ''; ?>">Buyers</a></li>
            <li><a href="?tab=products" class="<?php echo $currentTab === 'products' ? 'active' : ''; ?>">Products</a></li>
            <li><a href="?tab=orders" class="<?php echo $currentTab === 'orders' ? 'active' : ''; ?>">Orders</a></li>
        </ul>
    </nav>

    <div class="content">
        <?php if ($currentTab === 'dashboard'): ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Users</h3>
                    <div class="number"><?php echo $totalUsers; ?></div>
                </div>
                <div class="stat-card">
                    <h3>Merchants</h3>
                    <div class="number"><?php echo $totalMerchants; ?></div>
                </div>
                <div class="stat-card">
                    <h3>Buyers</h3>
                    <div class="number"><?php echo $totalBuyers; ?></div>
                </div>
                <div class="stat-card" style="background: #fff3cd;">
                    <h3>Pending Users</h3>
                    <div class="number" style="color: #856404;"><?php echo $pendingUsers; ?></div>
                </div>
                <div class="stat-card">
                    <h3>Total Products</h3>
                    <div class="number"><?php echo $totalProducts; ?></div>
                </div>
                <div class="stat-card">
                    <h3>Total Orders</h3>
                    <div class="number"><?php echo $totalOrders; ?></div>
                </div>
                <div class="stat-card" style="background: #fff3cd;">
                    <h3>Pending Products</h3>
                    <div class="number" style="color: #856404;"><?php echo $pendingProducts; ?></div>
                </div>
                <div class="stat-card" style="background: #fff3cd;">
                    <h3>Pending Orders</h3>
                    <div class="number" style="color: #856404;"><?php echo $pendingOrders; ?></div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($currentTab === 'users'): ?>
            <?php
            $stmt = $db->query("SELECT * FROM users ORDER BY created_at DESC");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Country</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo $user['id']; ?></td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td><?php echo htmlspecialchars($user['phone_number']); ?></td>
                            <td><?php echo htmlspecialchars($user['country']); ?></td>
                            <td><?php echo ucfirst($user['role']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $user['status'] ?? 'approved'; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $user['status'] ?? 'approved')); ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                    <select name="status" onchange="this.form.submit()">
                                        <option value="pending" <?php echo ($user['status'] ?? 'approved') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo ($user['status'] ?? 'approved') === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo ($user['status'] ?? 'approved') === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                    <input type="hidden" name="update_user_status" value="1">
                                </form>
                                <form method="POST" style="display: inline; margin-left: 10px;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                    <button type="submit" name="delete_user" class="btn btn-danger">Delete</button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($currentTab === 'merchants'): ?>
            <?php
            $stmt = $db->query("SELECT * FROM users WHERE role = 'merchant' ORDER BY created_at DESC");
            $merchants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            ?>
            <div class="table-container">
                <h2>Merchant Management</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Country</th>
                            <th>Status</th>
                            <th>Commercial Register</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($merchants as $merchant): ?>
                        <tr>
                            <td><?php echo $merchant['id']; ?></td>
                            <td><?php echo htmlspecialchars($merchant['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($merchant['email']); ?></td>
                            <td><?php echo htmlspecialchars($merchant['phone_number']); ?></td>
                            <td><?php echo htmlspecialchars($merchant['country']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $merchant['status'] ?? 'approved'; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $merchant['status'] ?? 'approved')); ?>
                                </span>
                            </td>
                            <td>
                                <?php if (!empty($merchant['commercial_register_photo'])): ?>
                                    <a href="../uploads/commercial_registers/<?php echo $merchant['commercial_register_photo']; ?>" target="_blank" class="btn btn-primary">View</a>
                                <?php else: ?>
                                    <span style="color: #999;">No file</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo date('Y-m-d', strtotime($merchant['created_at'])); ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="user_id" value="<?php echo $merchant['id']; ?>">
                                    <select name="status" onchange="this.form.submit()">
                                        <option value="pending" <?php echo ($merchant['status'] ?? 'approved') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo ($merchant['status'] ?? 'approved') === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo ($merchant['status'] ?? 'approved') === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                    <input type="hidden" name="update_user_status" value="1">
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($currentTab === 'buyers'): ?>
            <?php
            $stmt = $db->query("SELECT * FROM users WHERE role = 'buyer' ORDER BY created_at DESC");
            $buyers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            ?>
            <div class="table-container">
                <h2>Buyer Management</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Country</th>
                            <th>Status</th>
                            <th>Commercial Register</th>
                            <th>Shipping Address</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($buyers as $buyer): ?>
                        <tr>
                            <td><?php echo $buyer['id']; ?></td>
                            <td><?php echo htmlspecialchars($buyer['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($buyer['email']); ?></td>
                            <td><?php echo htmlspecialchars($buyer['phone_number']); ?></td>
                            <td><?php echo htmlspecialchars($buyer['country']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $buyer['status'] ?? 'approved'; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $buyer['status'] ?? 'approved')); ?>
                                </span>
                            </td>
                            <td>
                                <?php if (!empty($buyer['commercial_register_photo'])): ?>
                                    <a href="../uploads/commercial_registers/<?php echo $buyer['commercial_register_photo']; ?>" target="_blank" class="btn btn-primary">View</a>
                                <?php else: ?>
                                    <span style="color: #999;">No file</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars(substr($buyer['shipping_address'], 0, 50)) . '...'; ?></td>
                            <td><?php echo date('Y-m-d', strtotime($buyer['created_at'])); ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="user_id" value="<?php echo $buyer['id']; ?>">
                                    <select name="status" onchange="this.form.submit()">
                                        <option value="pending" <?php echo ($buyer['status'] ?? 'approved') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo ($buyer['status'] ?? 'approved') === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo ($buyer['status'] ?? 'approved') === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                    <input type="hidden" name="update_user_status" value="1">
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($currentTab === 'products'): ?>
            <?php
            $stmt = $db->query("
                SELECT p.*, u.full_name as merchant_name
                FROM products p
                LEFT JOIN users u ON p.user_id = u.id
                ORDER BY p.created_at DESC
            ");
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            ?>
            <div class="table-container">
                <h2>Product Management</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Image</th>
                            <th>Description</th>
                            <th>Merchant</th>
                            <th>Type</th>
                            <th>Price</th>
                            <th>Country</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td><?php echo $product['id']; ?></td>
                            <td>
                                <?php if (!empty($product['image'])): ?>
                                    <img src="../uploads/products/<?php echo $product['image']; ?>"
                                         alt="Product Image"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                                         onclick="showImageModal('../uploads/products/<?php echo $product['image']; ?>')">
                                <?php else: ?>
                                    <span style="color: #999;">No image</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div style="max-width: 200px;">
                                    <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                    <?php if (strlen($product['description']) > 100): ?>...<?php endif; ?>
                                </div>
                                <button onclick="showProductModal(<?php echo htmlspecialchars(json_encode($product)); ?>)" class="btn btn-primary" style="margin-top: 5px; font-size: 0.8rem;">View Details</button>
                            </td>
                            <td><?php echo htmlspecialchars($product['merchant_name']); ?></td>
                            <td><?php echo ucfirst($product['type']); ?></td>
                            <td>$<?php echo number_format($product['price'], 2); ?></td>
                            <td><?php echo htmlspecialchars($product['country_of_origin']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $product['status']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $product['status'])); ?>
                                </span>
                            </td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                    <select name="status" onchange="this.form.submit()">
                                        <option value="under_review" <?php echo $product['status'] === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                                        <option value="approved" <?php echo $product['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo $product['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                    <input type="hidden" name="update_product_status" value="1">
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($currentTab === 'orders'): ?>
            <?php
            $stmt = $db->query("
                SELECT o.*, p.description as product_description, p.price,
                       u.full_name as buyer_name, m.full_name as merchant_name
                FROM orders o
                LEFT JOIN products p ON o.product_id = p.id
                LEFT JOIN users u ON o.buyer_id = u.id
                LEFT JOIN users m ON p.user_id = m.id
                ORDER BY o.created_at DESC
            ");
            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
            ?>
            <div class="table-container">
                <h2>Order Management</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Product</th>
                            <th>Buyer</th>
                            <th>Merchant</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><?php echo $order['id']; ?></td>
                            <td>
                                <div style="max-width: 150px;">
                                    <?php echo htmlspecialchars(substr($order['product_description'], 0, 50)); ?>
                                    <?php if (strlen($order['product_description']) > 50): ?>...<?php endif; ?>
                                </div>
                                <button onclick="showOrderModal(<?php echo htmlspecialchars(json_encode($order)); ?>)" class="btn btn-primary" style="margin-top: 5px; font-size: 0.8rem;">View Details</button>
                            </td>
                            <td><?php echo htmlspecialchars($order['buyer_name']); ?></td>
                            <td><?php echo htmlspecialchars($order['merchant_name']); ?></td>
                            <td>$<?php echo number_format($order['price'], 2); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d', strtotime($order['created_at'])); ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                    <select name="status" onchange="this.form.submit()">
                                        <option value="under_review" <?php echo $order['status'] === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                                        <option value="approved" <?php echo $order['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo $order['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                    <input type="hidden" name="update_order_status" value="1">
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8);">
        <div style="position: relative; margin: 5% auto; width: 80%; max-width: 800px;">
            <span onclick="closeImageModal()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
            <img id="modalImage" style="width: 100%; height: auto; border-radius: 8px;">
        </div>
    </div>

    <!-- Product Details Modal -->
    <div id="productModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; margin: 5% auto; width: 80%; max-width: 600px; background: white; border-radius: 8px; padding: 20px;">
            <span onclick="closeProductModal()" style="position: absolute; top: 10px; right: 20px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
            <h2>Product Details</h2>
            <div id="productDetails"></div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div id="orderModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; margin: 5% auto; width: 80%; max-width: 600px; background: white; border-radius: 8px; padding: 20px;">
            <span onclick="closeOrderModal()" style="position: absolute; top: 10px; right: 20px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
            <h2>Order Details</h2>
            <div id="orderDetails"></div>
        </div>
    </div>

    <script>
        function showImageModal(imageSrc) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function showProductModal(product) {
            const details = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                    <div><strong>ID:</strong> ${product.id}</div>
                    <div><strong>Type:</strong> ${product.type.charAt(0).toUpperCase() + product.type.slice(1)}</div>
                    <div><strong>Price:</strong> $${parseFloat(product.price).toFixed(2)}</div>
                    <div><strong>Country of Origin:</strong> ${product.country_of_origin}</div>
                    <div><strong>Merchant:</strong> ${product.merchant_name}</div>
                    <div><strong>Status:</strong> <span class="status-badge status-${product.status}">${product.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span></div>
                    <div><strong>Created:</strong> ${new Date(product.created_at).toLocaleDateString()}</div>
                    <div><strong>Updated:</strong> ${new Date(product.updated_at).toLocaleDateString()}</div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Description:</strong>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">
                        ${product.description}
                    </div>
                </div>
                ${product.image ? `
                <div style="margin-top: 15px;">
                    <strong>Product Image:</strong>
                    <div style="margin-top: 5px;">
                        <img src="../uploads/products/${product.image}" alt="Product Image" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer;" onclick="showImageModal('../uploads/products/${product.image}')">
                    </div>
                </div>
                ` : ''}
            `;
            document.getElementById('productDetails').innerHTML = details;
            document.getElementById('productModal').style.display = 'block';
        }

        function closeProductModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        function showOrderModal(order) {
            const details = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                    <div><strong>Order ID:</strong> ${order.id}</div>
                    <div><strong>Product ID:</strong> ${order.product_id}</div>
                    <div><strong>Buyer:</strong> ${order.buyer_name}</div>
                    <div><strong>Merchant:</strong> ${order.merchant_name}</div>
                    <div><strong>Price:</strong> $${parseFloat(order.price).toFixed(2)}</div>
                    <div><strong>Status:</strong> <span class="status-badge status-${order.status}">${order.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span></div>
                    <div><strong>Order Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</div>
                    <div><strong>Last Updated:</strong> ${new Date(order.updated_at).toLocaleDateString()}</div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Product Description:</strong>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">
                        ${order.product_description}
                    </div>
                </div>
            `;
            document.getElementById('orderDetails').innerHTML = details;
            document.getElementById('orderModal').style.display = 'block';
        }

        function closeOrderModal() {
            document.getElementById('orderModal').style.display = 'none';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const imageModal = document.getElementById('imageModal');
            const productModal = document.getElementById('productModal');
            const orderModal = document.getElementById('orderModal');

            if (event.target == imageModal) {
                closeImageModal();
            }
            if (event.target == productModal) {
                closeProductModal();
            }
            if (event.target == orderModal) {
                closeOrderModal();
            }
        }
    </script>
</body>
</html>
