{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\ALraid\\al_raid_flutter_app\\android\\app\\.cxx\\RelWithDebInfo\\3m5k2u4p\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\ALraid\\al_raid_flutter_app\\android\\app\\.cxx\\RelWithDebInfo\\3m5k2u4p\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}