// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.7.3") // Updated to match settings.gradle.kts and support NDK 26.3.11579264
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:2.1.0") // Updated to match settings.gradle.kts
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // Force all subprojects to use the same NDK version
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            project.android {
                if (hasProperty("ndkVersion")) {
                    ndkVersion = "26.3.11579264"
                }
            }
        }
    }
}

tasks.register<Delete>("clean") {
    delete(rootProject.buildDir)
}
